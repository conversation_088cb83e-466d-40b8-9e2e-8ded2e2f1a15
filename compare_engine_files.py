#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to compare multiple modules between vanilla and modified versions
"""
import os
import subprocess

def get_file_list(directory):
    """Get list of all files in directory recursively"""
    files = set()
    for root, dirs, filenames in os.walk(directory):
        for filename in filenames:
            # Get relative path from the module directory
            rel_path = os.path.relpath(os.path.join(root, filename), directory)
            files.add(rel_path.replace('\\', '/'))  # Normalize path separators
    return files

def compare_file_content(file1, file2):
    """Compare two files and return True if they differ"""
    try:
        with open(file1, 'rb') as f1, open(file2, 'rb') as f2:
            return f1.read() != f2.read()
    except:
        return True  # If we can't read, assume they differ

def compare_module(module_name):
    """Compare a specific module between vanilla and modified versions"""
    vanilla_module = f"UHTHeaderDumpVanilla_5_5/{module_name}"
    modified_module = f"UHT_14Beta1/{module_name}"

    # Check if both modules exist
    if not os.path.exists(vanilla_module) or not os.path.exists(modified_module):
        return None

    print(f"=== COMPARING {module_name.upper()} MODULE ===")

    # Get file lists
    vanilla_files = get_file_list(vanilla_module)
    modified_files = get_file_list(modified_module)

    # Find differences
    only_in_vanilla = vanilla_files - modified_files
    only_in_modified = modified_files - vanilla_files
    common_files = vanilla_files & modified_files

    print(f"Files only in vanilla: {len(only_in_vanilla)}")
    print(f"Files only in modified: {len(only_in_modified)}")
    print(f"Common files: {len(common_files)}")

    # Check for content differences in common files
    different_files = []
    for file in sorted(common_files):
        vanilla_path = os.path.join(vanilla_module, file)
        modified_path = os.path.join(modified_module, file)

        if compare_file_content(vanilla_path, modified_path):
            different_files.append(file)

    print(f"Files with content differences: {len(different_files)}")

    result = {
        'module': module_name,
        'only_in_vanilla': only_in_vanilla,
        'only_in_modified': only_in_modified,
        'different_files': different_files,
        'total_files_vanilla': len(vanilla_files),
        'total_files_modified': len(modified_files)
    }

    if only_in_modified:
        print("\n=== NEW FILES IN MODIFIED ===")
        for file in sorted(only_in_modified)[:20]:  # Limit output
            print(f"  + {file}")
        if len(only_in_modified) > 20:
            print(f"  ... and {len(only_in_modified) - 20} more files")

    if different_files:
        print("\n=== FILES WITH CONTENT DIFFERENCES ===")
        for file in different_files[:20]:  # Limit output
            print(f"  * {file}")
        if len(different_files) > 20:
            print(f"  ... and {len(different_files) - 20} more files")

    print()
    return result

def main():
    # Core modules to compare
    core_modules = [
        'Engine', 'CoreUObject', 'Renderer', 'Slate', 'SlateCore',
        'InputCore', 'RHI', 'PhysicsCore', 'Chaos', 'NetCore',
        'GameplayTags', 'GameplayTasks', 'AIModule', 'NavigationSystem'
    ]

    results = []
    for module in core_modules:
        result = compare_module(module)
        if result:
            results.append(result)

    # Summary
    print("=== SUMMARY ===")
    for result in results:
        print(f"{result['module']}: {len(result['different_files'])} changed files, "
              f"{len(result['only_in_modified'])} new files")

if __name__ == "__main__":
    main()
