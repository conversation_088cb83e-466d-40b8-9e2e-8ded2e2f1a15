#pragma once
#include "CoreMinimal.h"
#include "BTService_BlackboardBase.h"
#include "EQSParametrizedQueryExecutionRequest.h"
#include "BTService_RunEQS.generated.h"

UCLASS(Blueprintable, MinimalAPI)
class UBTService_RunEQS : public UBTService_BlackboardBase {
    GENERATED_BODY()
public:
protected:
    UPROPERTY(EditAnywhere, meta=(AllowPrivateAccess=true))
    FEQSParametrizedQueryExecutionRequest EQSRequest;
    
    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
    bool bUpdateBBOnFail;
    
public:
    UBTService_RunEQS();

};

