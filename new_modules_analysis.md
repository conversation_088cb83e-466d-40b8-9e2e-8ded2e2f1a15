# New Modules in Modified UE5.5 Version

This document lists and categorizes all the new modules that exist only in the modified version (UHT_14Beta1) and not in vanilla UE5.5.

## Game-Specific Modules

### IntoTheRadius2
- **Purpose**: Main game module for "Into The Radius 2" VR game
- **Files**: 461+ files (Private) + 640+ files (Public)
- **Key Features**: 
  - AI systems, anomalies, artifacts
  - Weapon systems, attachments, ammunition
  - VR-specific gameplay mechanics
  - Save/load systems, missions, inventory

### ITR_MobileShaders
- **Purpose**: Custom mobile shader implementations for the game
- **Likely Function**: Optimized rendering for mobile VR platforms

## Animation & Locomotion Systems

### ALS (Advanced Locomotion System)
- **Purpose**: Advanced character movement and animation system
- **Files**: 60+ files for character locomotion, mantling, ragdolling
- **Key Features**:
  - Advanced character movement states
  - Mantling and climbing systems
  - Ragdoll physics integration
  - Animation blending and state management

### ALSCamera
- **Purpose**: Camera system for ALS
- **Function**: Camera management for advanced locomotion

### ALSExtras
- **Purpose**: Additional features for ALS
- **Function**: Extended functionality for the locomotion system

### AnimationBudgetAllocator
- **Purpose**: Animation performance optimization
- **Function**: Manages animation update budgets for performance

### AnimationLocomotionLibraryRuntime
- **Purpose**: Runtime locomotion animation library
- **Function**: Provides locomotion animation utilities

### AnimationWarpingRuntime
- **Purpose**: Animation warping system
- **Function**: Real-time animation modification and warping

### MotionWarping
- **Purpose**: Motion warping for animations
- **Function**: Adjusts root motion to match target positions

### PoseSearch
- **Purpose**: Pose matching and search system
- **Function**: Finds best matching poses for animation transitions

## VR & XR Systems

### VRExpansionPlugin
- **Purpose**: Comprehensive VR interaction system
- **Files**: 120+ files for VR interactions
- **Key Features**:
  - VR grip and interaction systems
  - Physics-based VR interactions
  - VR-specific AI sight systems
  - Hand tracking and gesture recognition

### OpenXRExpansionPlugin
- **Purpose**: Extended OpenXR functionality
- **Function**: Additional OpenXR features beyond standard implementation

## Networking & Online Systems

### AdvancedSessions
- **Purpose**: Enhanced session management
- **Function**: Advanced multiplayer session handling

### AdvancedSteamSessions
- **Purpose**: Steam-specific session management
- **Function**: Steam platform integration for multiplayer

### OnlineSubsystemSteam
- **Purpose**: Steam platform integration
- **Function**: Steam API integration for achievements, friends, etc.

### NetworkPrediction
- **Purpose**: Network prediction system
- **Function**: Client-side prediction for networked gameplay

## AI & Behavior Systems

### AICoverSystem
- **Purpose**: AI cover system
- **Function**: AI behavior for taking and using cover

### HTN (Hierarchical Task Network)
- **Purpose**: Advanced AI planning system
- **Function**: Hierarchical task planning for AI behavior

## Gameplay Systems

### GameplayAbilities
- **Purpose**: Gameplay ability system
- **Function**: Modular ability and effect system

### Mover
- **Purpose**: Advanced movement system
- **Function**: Enhanced character movement capabilities

## Rendering & Graphics

### ChaosClothAssetEngine
- **Purpose**: Cloth simulation asset management
- **Function**: Chaos physics cloth asset handling

### ChaosVehicles, ChaosVehiclesCore, ChaosVehiclesEngine
- **Purpose**: Vehicle physics system
- **Function**: Chaos physics-based vehicle simulation

### ColorCorrectRegions
- **Purpose**: Regional color correction
- **Function**: Area-based color grading and correction

### OpenColorIO
- **Purpose**: Color management system
- **Function**: Professional color pipeline management

## Procedural Content Generation

### PCG (Procedural Content Generation)
- **Purpose**: Procedural content generation framework
- **Function**: Runtime procedural content creation

### PCGGeometryScriptInterop
- **Purpose**: PCG geometry script integration
- **Function**: Bridges PCG with geometry scripting

### GeometryScriptingCore
- **Purpose**: Core geometry scripting system
- **Function**: Runtime geometry manipulation and creation

## Environment & World Systems

### Landmass
- **Purpose**: Large-scale terrain system
- **Function**: Massive world terrain management

### Water
- **Purpose**: Water simulation system
- **Function**: Advanced water rendering and simulation

### Soundscape
- **Purpose**: Environmental audio system
- **Function**: Dynamic environmental sound management

## Development & Tools

### BlendStack
- **Purpose**: Animation blending system
- **Function**: Advanced animation layer blending

### ConsoleVariablesEditorRuntime
- **Purpose**: Runtime console variable management
- **Function**: Dynamic console variable editing

### DataRegistry
- **Purpose**: Data asset registry system
- **Function**: Centralized data asset management

### DisplayClusterLightCardExtender
- **Purpose**: nDisplay light card extensions
- **Function**: Extended light card functionality for LED walls

## Movie Rendering

### MovieRenderPipelineCore
- **Purpose**: Core movie rendering system
- **Function**: High-quality offline rendering pipeline

### MovieRenderPipelineRenderPasses
- **Purpose**: Render passes for movie pipeline
- **Function**: Individual render pass implementations

### MovieRenderPipelineSettings
- **Purpose**: Movie pipeline configuration
- **Function**: Settings and configuration for movie rendering

## Analytics & Telemetry

### AnalyticsBlueprintLibrary
- **Purpose**: Blueprint analytics integration
- **Function**: Analytics functionality accessible from Blueprints

### CmAnalytics
- **Purpose**: Custom analytics system
- **Function**: Game-specific analytics and telemetry

### Sentry
- **Purpose**: Error reporting and crash analytics
- **Function**: Sentry.io integration for error tracking

## Summary

**Total New Modules**: 42

The modified version includes significant additions focused on:
1. **VR/XR Gaming**: Comprehensive VR interaction systems
2. **Advanced Animation**: Multiple locomotion and animation systems
3. **Game-Specific Content**: "Into The Radius 2" game modules
4. **Enhanced Rendering**: Mobile optimizations and advanced graphics features
5. **Multiplayer/Networking**: Steam integration and advanced session management
6. **Procedural Generation**: PCG framework and geometry scripting
7. **Professional Tools**: Movie rendering pipeline and analytics systems

These additions suggest this is a heavily modified engine specifically tailored for a VR game with advanced animation systems, multiplayer capabilities, and professional content creation tools.
