#include "PawnAction_Move.h"

UDEPRECATED_PawnAction_Move::UDEPRECATED_PawnAction_Move() {
    this->bShouldPauseMovement = true;
    this->bAlwaysNotifyOnFinished = true;
    this->GoalActor = NULL;
    this->AcceptableRadius = 30.00f;
    this->FilterClass = NULL;
    this->bAllowStrafe = false;
    this->bFinishOnOverlap = true;
    this->bUsePathfinding = true;
    this->bAllowPartialPath = true;
    this->bProjectGoalToNavigation = false;
    this->bUpdatePathToGoal = true;
    this->bAbortSubActionOnPathChange = false;
}


