#pragma once
#include "CoreMinimal.h"
//CROSS-MODULE INCLUDE V2: -ModuleName=Engine -ObjectName=AnimCurveCompressionCodec -FallbackName=AnimCurveCompressionCodec
#include "AnimCurveCompressionCodec_ACL.generated.h"

UCLASS(Blueprintable, EditInlineNew, MinimalAPI)
class UAnimCurveCompressionCodec_ACL : public UAnimCurveCompressionCodec {
    GENERATED_BODY()
public:
    UAnimCurveCompressionCodec_ACL();

};

