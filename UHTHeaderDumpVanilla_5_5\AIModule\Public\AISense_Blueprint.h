#pragma once
#include "CoreMinimal.h"
#include "AISense.h"
#include "Templates/SubclassOf.h"
#include "AISense_Blueprint.generated.h"

class AActor;
class APawn;
class UAIPerceptionComponent;
class UAISenseEvent;
class UUserDefinedStruct;

UCLASS(Abstract, Blueprintable, HideDropdown, MinimalAPI)
class UAISense_Blueprint : public UAISense {
    GENERATED_BODY()
public:
protected:
    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
    TSubclassOf<UUserDefinedStruct> ListenerDataType;
    
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Instanced, meta=(AllowPrivateAccess=true))
    TArray<UAIPerceptionComponent*> ListenerContainer;
    
    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(AllowPrivateAccess=true))
    TArray<UAISenseEvent*> UnprocessedEvents;
    
public:
    UAISense_Blueprint();

    UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
    float OnUpdate(const TArray<UAISenseEvent*>& EventsToProcess);
    
    UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
    void OnListenerUpdated(AActor* ActorListener, UAIPerceptionComponent* PerceptionComponent);
    
    UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
    void OnListenerUnregistered(AActor* ActorListener, UAIPerceptionComponent* PerceptionComponent);
    
    UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
    void OnListenerRegistered(AActor* ActorListener, UAIPerceptionComponent* PerceptionComponent);
    
    UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
    void K2_OnNewPawn(APawn* NewPawn);
    
    UFUNCTION(BlueprintCallable, BlueprintPure)
    void GetAllListenerComponents(TArray<UAIPerceptionComponent*>& ListenerComponents) const;
    
    UFUNCTION(BlueprintCallable, BlueprintPure)
    void GetAllListenerActors(TArray<AActor*>& ListenerActors) const;
    
};

