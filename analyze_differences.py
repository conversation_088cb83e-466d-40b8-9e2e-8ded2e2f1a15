#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to analyze differences between vanilla UE5.5 and modified version
"""

def read_modules(filename):
    """Read module names from file, removing trailing slashes"""
    with open(filename, 'r') as f:
        modules = [line.strip().rstrip('/') for line in f if line.strip()]
    return set(modules)

def main():
    # Read module lists
    vanilla_modules = read_modules('vanilla_modules.txt')
    modified_modules = read_modules('modified_modules.txt')
    
    # Find differences
    only_in_vanilla = vanilla_modules - modified_modules
    only_in_modified = modified_modules - vanilla_modules
    common_modules = vanilla_modules & modified_modules
    
    print("=== MODULE ANALYSIS ===")
    print(f"Vanilla UE5.5 modules: {len(vanilla_modules)}")
    print(f"Modified version modules: {len(modified_modules)}")
    print(f"Common modules: {len(common_modules)}")
    print(f"Only in vanilla: {len(only_in_vanilla)}")
    print(f"Only in modified: {len(only_in_modified)}")
    print()
    
    print("=== MODULES ONLY IN VANILLA UE5.5 ===")
    for module in sorted(only_in_vanilla):
        print(f"  - {module}")
    print()
    
    print("=== NEW MODULES IN MODIFIED VERSION ===")
    for module in sorted(only_in_modified):
        print(f"  + {module}")
    print()
    
    print("=== COMMON MODULES (POTENTIAL MODIFICATIONS) ===")
    print(f"Total: {len(common_modules)} modules")
    for module in sorted(common_modules):
        print(f"  = {module}")

if __name__ == "__main__":
    main()
