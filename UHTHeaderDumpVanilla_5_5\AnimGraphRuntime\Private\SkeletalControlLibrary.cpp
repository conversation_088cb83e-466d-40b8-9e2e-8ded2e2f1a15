#include "SkeletalControlLibrary.h"

USkeletalControlLibrary::USkeletalControlLibrary() {
}

FSkeletalControlReference USkeletalControlLibrary::Set<PERSON><PERSON><PERSON>(const FSkeletalControlReference& SkeletalControl, float Alpha) {
    return FSkeletalControlReference{};
}

float USkeletalControlLibrary::Get<PERSON><PERSON><PERSON>(const FSkeletalControlReference& SkeletalControl) {
    return 0.0f;
}

void USkeletalControlLibrary::ConvertToSkeletalControlPure(const FAnimNodeReference& Node, FSkeletalControlReference& SkeletalControl, bool& Result) {
}

FSkeletalControlReference USkeletalControlLibrary::ConvertToSkeletalControl(const FAnimNodeReference& Node, EAnimNodeReferenceConversionResult& Result) {
    return FSkeletalControlReference{};
}


