#pragma once
#include "CoreMinimal.h"
//CROSS-MODULE INCLUDE V2: -ModuleName=Engine -ObjectName=AnimBoneCompressionCodec -FallbackName=AnimBoneCompressionCodec
#include "AnimBoneCompressionCodec_ACLBase.generated.h"

UCLASS(Abstract, Blueprintable, EditInlineNew, MinimalAPI)
class UAnimBoneCompressionCodec_ACLBase : public UAnimBoneCompressionCodec {
    GENERATED_BODY()
public:
    UAnimBoneCompressionCodec_ACLBase();

};

